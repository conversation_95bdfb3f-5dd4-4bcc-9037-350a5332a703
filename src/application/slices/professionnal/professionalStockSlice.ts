import {
  createSlice,
  createAsyncThunk,
  PayloadAction,
  Draft,
} from "@reduxjs/toolkit";
import { Stocks } from "@/domain/models";
import GetProfessionalStocksRepository from "@/infrastructure/repositories/stocks/GetProfessionalStocksRepository";
import GetProfessionalStocksUsecase from "@/domain/usecases/stocks/GetProfessionalStocksUsecase";
import CreateProfessionalStocksUsecase from "@/domain/usecases/stocks/CreateProfessionalStocksUsecase";
import CreateProfessionalStocksRepository from "@/infrastructure/repositories/stocks/CreateProfessionalStocksRepository";
import UpdateProfessionalStockRepository from "@/infrastructure/repositories/stocks/UpdateProfessionalStockRepository.ts";
import UpdateProfessionalStockUsecase from "@/domain/usecases/stocks/UpdateProfessionalStocksUsecase.ts";
import DeleteProfessionalStockUsecase from "@/domain/usecases/stocks/DeleteProfessionalStockUsecase.ts";
import DeleteProfessionalStockRepository from "@/infrastructure/repositories/stocks/DeleteProfessionalStockRepository.ts";
import GetProfessionalStockByStockIdRepository from "@/infrastructure/repositories/stocks/GetProfessionalStockByStockIdRepository.ts";
import GetProfessionalStockByStockIdUsecase from "@/domain/usecases/stocks/GetProfessionalStocksByStockIdUsecase.ts";

export type FormData = Omit<Stocks, "id">;

export type FormDataKey = keyof Omit<FormData, "id">;

type Payload<K extends FormDataKey = FormDataKey> = {
  key: K;
  value: FormData[K];
};

interface StocksSliceState {
  stocks: Stocks[];
  selectedStock: Stocks | null;
  formData: Omit<Stocks, "id"> | null;
  loading: boolean;
  formErrors: { [K in keyof FormData]?: string };
  error: string;
}

const DEFAULT_STOCK_STATE = {
  id_sous_categorie: null,
  id_professionnel: null,
  est_supprimee: false,
  nom_produit_sante: "",
  quantite_totale: null,
  montant_actuel: null,
  date_ajout: new Date().toISOString(),
  date_expiration: null,
  lot: "",
  prix: null,
  numero_serie: "",
  remarques: "",
};

function createInitialFormErrors<T extends object>(): {
  [K in keyof T]: string;
} {
  const keys = Object.keys({} as T) as Array<keyof T>;
  const result = {} as { [K in keyof T]: string };

  for (const key of keys) {
    result[key] = "";
  }

  return result;
}

const initialState: StocksSliceState = {
  stocks: [],
  selectedStock: null,
  formData: DEFAULT_STOCK_STATE,
  loading: false,
  error: null,
  formErrors: createInitialFormErrors<FormData>(),
};

export const createStocksByProfessionalIdSlice = createAsyncThunk(
  "stocks/create",
  async (data: Omit<Stocks, "id">[], { rejectWithValue }) => {
    try {
      const createProfessionalStocksRepository =
        new CreateProfessionalStocksRepository();
      const CreateProfessionalStocksUseCase =
        new CreateProfessionalStocksUsecase(createProfessionalStocksRepository);
      const result = await CreateProfessionalStocksUseCase.execute(data);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  },
);

export const getStocksByProfessionalIdSlice = createAsyncThunk(
  "stocks/getAll",
  async (professionalId: number, { rejectWithValue }) => {
    try {
      const getProfessinalStocksRepository =
        new GetProfessionalStocksRepository();
      const getProfessionalStocksUsecase = new GetProfessionalStocksUsecase(
        getProfessinalStocksRepository,
      );
      const result = await getProfessionalStocksUsecase.execute(professionalId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  },
);

export const getStockByIdSlice = createAsyncThunk(
  "stocks/get",
  async (professionalId: number, { rejectWithValue }) => {
    try {
      const getProfessionalStockByStockIdRepository =
        new GetProfessionalStockByStockIdRepository();
      const getProfessionalStocksUsecase =
        new GetProfessionalStockByStockIdUsecase(
          getProfessionalStockByStockIdRepository,
        );
      const result = await getProfessionalStocksUsecase.execute(professionalId);
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  },
);

export const updateStocksByProfessionalIdSlice = createAsyncThunk(
  "stocks/update",
  async (
    { stockId, data }: { stockId: number; data: Partial<Stocks> },
    { rejectWithValue },
  ) => {
    try {
      const updateProfessionalStockRepository =
        new UpdateProfessionalStockRepository();
      const updateProfessionalStockUsecase = new UpdateProfessionalStockUsecase(
        updateProfessionalStockRepository,
      );
      const { id, ...rest } = data;
      const result = await updateProfessionalStockUsecase.execute(
        stockId,
        rest,
      );
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  },
);

export const deleteStocksByProfessionalIdSlice = createAsyncThunk(
  "stocks/delete",
  async (
    {
      professionalId,
      estSupprimee,
    }: { professionalId: number; estSupprimee: boolean },
    { rejectWithValue },
  ) => {
    try {
      const deleteProfessionalStockRepository =
        new DeleteProfessionalStockRepository();
      const deleteProfessionalStockUsecase = new DeleteProfessionalStockUsecase(
        deleteProfessionalStockRepository,
      );
      const result = await deleteProfessionalStockUsecase.execute(
        professionalId,
        estSupprimee,
      );
      return result;
    } catch (error) {
      return rejectWithValue((error as Error).message);
    }
  },
);

const StocksSlice = createSlice({
  name: "stocks",
  initialState,
  reducers: {
    setSelectedStockSlice: (state, action) => {
      state.selectedStock = action.payload;
    },
    setFormData: <K extends FormDataKey>(
      state: Draft<StocksSliceState>,
      action: PayloadAction<Payload<K>>,
    ) => {
      if (state.formData) {
        state.formData[action.payload.key] = action.payload.value;
      }
    },
    initializeData: (
      state: Draft<StocksSliceState>,
      action: PayloadAction<{ matchingStock: Stocks }>,
    ) => {
      if (!action.payload.matchingStock) {
        state.error = "Stock non trouvé.";
      } else {
        state.formData = action.payload.matchingStock;
      }
    },
    resetState: (state) => {
      state.formData = DEFAULT_STOCK_STATE;
    },
    clearSelectedStockSlice: (state) => {
      state.selectedStock = null;
    },

    setFormErrors: (
      state,
      action: PayloadAction<{ [key: string]: string }>,
    ) => {
      state.formErrors = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Create
      .addCase(createStocksByProfessionalIdSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createStocksByProfessionalIdSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.stocks.push(...action.payload);
      })
      .addCase(createStocksByProfessionalIdSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get All
      .addCase(getStocksByProfessionalIdSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStocksByProfessionalIdSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.stocks = action.payload;
      })
      .addCase(getStocksByProfessionalIdSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Get by stockId
      .addCase(getStockByIdSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getStockByIdSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedStock = action.payload;
      })
      .addCase(getStockByIdSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Update
      .addCase(updateStocksByProfessionalIdSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateStocksByProfessionalIdSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.selectedStock = action.payload;
        state.stocks = state.stocks.map((stock) => {
          const isTargetStock = stock.id === action.payload.id;

          return isTargetStock ? action.payload : stock;
        });
      })
      .addCase(updateStocksByProfessionalIdSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // Delete
      .addCase(deleteStocksByProfessionalIdSlice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteStocksByProfessionalIdSlice.fulfilled, (state, action) => {
        state.loading = false;
        state.stocks = state.stocks.filter(
          (st) => st.id !== Number(action.payload),
        );
      })
      .addCase(deleteStocksByProfessionalIdSlice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setSelectedStockSlice,
  setFormData,
  clearSelectedStockSlice,
  resetState,
  initializeData,
  setFormErrors,
} = StocksSlice.actions;
export default StocksSlice.reducer;
