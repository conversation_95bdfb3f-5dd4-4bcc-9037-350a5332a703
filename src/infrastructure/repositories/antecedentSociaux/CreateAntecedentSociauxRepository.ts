import { Antecedant_sociaux } from '@/domain/models'
import { ICreateAntecedentSociauxRepository } from '@/domain/interfaces/repositories/antecedentSociaux'
import { supabase } from '@/infrastructure/supabase/supabase'
import { handleError } from '@/infrastructure/supabase/supabaseFetchError'
import { ANTECEDENT_SOCIAUX_TABLE_NAME } from './Constant'

export class CreateAntecedentSociauxRepository implements ICreateAntecedentSociauxRepository {
  async create(data: Antecedant_sociaux): Promise<Antecedant_sociaux> {
    const { data: createdData, error } = await supabase
      .from(ANTECEDENT_SOCIAUX_TABLE_NAME)
      .insert(data)
      .select()
      .single()

    if (error) {
      handleError(error)
    }

    return createdData
  }
}
