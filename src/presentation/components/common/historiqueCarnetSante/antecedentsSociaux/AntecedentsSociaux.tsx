import { useAntecedentSociaux } from "@/presentation/hooks/carnetDeSante/sousCarnet/useAntecedentSociaux";
import {
  Autocomplete,
  Box,
  FormControl,
  FormControlLabel,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";

interface AntecedentsSociauxProps {
  item: string;
}

const Options = [
  "1 - 5 cigarettes",
  "5 - 10 cigarettes",
  "1 paquet",
  "2 paquets",
  "Socialement",
];
const OptionsAlcoolique = [
  "Quotidiennement",
  "Hebdomadaire",
  "Non fréquement/socialement",
];

const AntecedentsSociaux = ({ item }: AntecedentsSociauxProps) => {
  const {
    antecedentSociauxState,
    handleEstActiveChange,
    handltQualiteChange,
    handleRemarksChange,
  } = useAntecedentSociaux();
  return (
    <>
      <Typography variant="subtitle1" sx={{ mt: 3, mb: 1, fontWeight: "bold" }}>
        {item}
      </Typography>
      <Box sx={{ display: "flex", gap: 2, alignItems: "center", my: 1 }}>
        <Typography variant="body2" color="textSecondary">
          {item === "Fummeur"
            ? "Êtes-vous fumeur actif ?"
            : "Consommez-vous activement de l'alcool ?"}
        </Typography>
        <FormControl>
          <RadioGroup
            onChange={(e) => handleEstActiveChange(item, e.target.value)}
            row
          >
            <FormControlLabel
              value="oui"
              checked={antecedentSociauxState.estActive[item] === "oui"}
              control={<Radio size="small" />}
              label={<Typography variant="body2">Oui</Typography>}
            />
            <FormControlLabel
              value="non"
              checked={antecedentSociauxState.estActive[item] === "non"}
              control={<Radio size="small" />}
              label={<Typography variant="body2">Non</Typography>}
            />
          </RadioGroup>
        </FormControl>
      </Box>
      <Box sx={{ my: 1 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          {item === "Fummeur"
            ? "En quelle année avez-vous commencé à fumer ?"
            : "Combien de boissons consommez-vous ?"}
        </Typography>
        {item === "Fummeur" ? (
          <Autocomplete
            size="small"
            options={Options}
            renderInput={(params) => (
              <TextField {...params} placeholder={"Entrez l'année"} />
            )}
          />
        ) : (
          <TextField
            size="small"
            placeholder={"Entrer une quantité personnalisée"}
            fullWidth
          />
        )}
      </Box>
      <Box sx={{ my: 1 }}>
        <Typography
          variant="caption"
          color="textSecondary"
          sx={{ mb: 1, display: "block" }}
        >
          {item === "Fummeur"
            ? "Combien vous fumez chaque jour ?"
            : "À quelle fréquence vous buvez ?"}
        </Typography>
        <Autocomplete
          size="small"
          options={item === "Fummeur" ? Options : OptionsAlcoolique}
          renderInput={(params) => (
            <TextField {...params} placeholder="Definir la quantité" />
          )}
          value={antecedentSociauxState.qualite[item] || ""}
          onChange={(e, newValue) => handltQualiteChange(item, newValue || "")}
        />
      </Box>
      <TextField
        placeholder="Remarques"
        multiline
        rows={2}
        fullWidth
        sx={{ mt: 2 }}
        value={antecedentSociauxState.remarks[item] || ""}
        onChange={(e) => handleRemarksChange(item, e.target.value)}
      />
    </>
  );
};

export default AntecedentsSociaux;
