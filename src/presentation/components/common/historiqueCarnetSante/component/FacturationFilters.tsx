import React from "react";
import {
  Autocomplete,
  Avatar,
  Box,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import { Search } from "lucide-react";
import { PatientOption } from "@/presentation/hooks/facturation/useFacturationFilters";

/**
 * Props pour le composant FacturationFilters
 */
interface FacturationFiltersProps {
  /** Patient actuellement sélectionné */
  selectedPatient: PatientOption | null;
  /** Liste des patients disponibles pour le filtrage */
  availablePatients: PatientOption[];
  /** Callback appelé lors du changement de sélection */
  onPatientChange: (patient: PatientOption | null) => void;
  /** Indique si le composant est en cours de chargement */
  loading?: boolean;
}

/**
 * Composant de filtrage des facturations par patient
 *
 * Ce composant présente une interface utilisateur pour sélectionner un patient
 * et filtrer les facturations. Il ne contient que la logique de présentation.
 *
 * @param props - Les propriétés du composant
 */
const FacturationFilters: React.FC<FacturationFiltersProps> = ({
  selectedPatient,
  availablePatients,
  onPatientChange,
  loading = false,
}) => {
  return (
    <Autocomplete
      size="small"
      value={selectedPatient}
      onChange={(_, newValue) => onPatientChange(newValue)}
      options={availablePatients}
      className="w-full md:w-[250px]"
      getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
      clearOnEscape
      clearText="Effacer"
      noOptionsText="Aucun patient trouvé"
      disabled={loading}
      renderOption={(props, option) => (
        <Box
          component="li"
          sx={{ display: "flex", alignItems: "center", gap: 2 }}
          {...props}
          key={option.id}
        >
          <Avatar sx={{ width: 24, height: 24 }} />
          <Typography>
            {option.nom} {option.prenom}
          </Typography>
        </Box>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Chercher par le nom du patient..."
          slotProps={{
            input: {
              ...params.InputProps,
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
            },
          }}
        />
      )}
    />
  );
};

export default FacturationFilters;

// Ré-export de l'interface pour la compatibilité
export type { PatientOption } from "@/presentation/hooks/facturation/useFacturationFilters";
