import React from "react";
import {
  Autocomplete,
  Avatar,
  Box,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import { Search } from "lucide-react";
import { useFacturation } from "@/presentation/hooks/facturation";

export interface PatientOption {
  id: number;
  nom: string;
  prenom: string;
}

interface FacturationFiltersProps {
  selectedPatient: PatientOption;
  onSelectedPatientChange: (value: PatientOption) => void;
}

const FacturationFilters: React.FC<FacturationFiltersProps> = ({
  selectedPatient,
  onSelectedPatientChange,
}) => {
  const { listeFacturationProfessional, getFacturedUser } = useFacturation();
  const PATIENTS = getFacturedUser(listeFacturationProfessional);

  const handlePatientChange = (newValue: PatientOption | null) => {
    onSelectedPatientChange(newValue);
  };
  return (
    <Autocomplete
      size="small"
      value={selectedPatient}
      onChange={(_, newValue) => handlePatientChange(newValue)}
      options={PATIENTS}
      className="w-full md:w-[250px]"
      getOptionLabel={(option) => `${option.nom} ${option.prenom}`}
      renderOption={(props, option) => (
        <Box
          component="li"
          sx={{ display: "flex", alignItems: "center", gap: 2 }}
          {...props}
          key={option.id}
        >
          <Avatar sx={{ width: 24, height: 24 }} />
          <Typography>
            {option.nom} {option.prenom}
          </Typography>
        </Box>
      )}
      renderInput={(params) => (
        <TextField
          {...params}
          placeholder="Chercher par le nom du patient..."
          InputProps={{
            ...params.InputProps,
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      )}
    />
  );
};

export default FacturationFilters;
