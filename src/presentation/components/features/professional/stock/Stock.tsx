import { useState, useMemo, useEffect } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { Button, Typography } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import StockFilters from "./StockFilters";
import { AddStocksModal } from "@/presentation/components/common/Modal/AddStocksModal.tsx";
import { useProfessionalStock } from "@/presentation/hooks/professionalStock/useProfessionalStock.ts";

const Stock = () => {
  const { stocks, resetState, handleGetStocksByProfessionalId } =
    useProfessionalStock();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<boolean>(false);
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id,
  );
  const [isAddStockModalOpen, setIsAddStockModalOpen] =
    useState<boolean>(false);

  const filteredStocks = useMemo(() => {
    return stocks.filter((stock) => {
      const matchesStatus = stock.est_supprimee === selectedStatus;
      if (searchQuery) {
        const matchesSearch = stock.nom_produit_sante
          .toLowerCase()
          .includes(searchQuery.toLowerCase());

        return matchesSearch && matchesStatus;
      }
      return matchesStatus;
    });
  }, [stocks, searchQuery, selectedStatus]);

  const handleClose = () => {
    setIsAddStockModalOpen(false);
    resetState();
  };

  useEffect(() => {
    if (professionalId) {
      handleGetStocksByProfessionalId(professionalId);
    }
  }, [professionalId]);

  return (
    <div>
      <div>
        <Button
          variant="outlined"
          sx={{ textTransform: "none", mb: 1 }}
          onClick={() => setIsAddStockModalOpen(!isAddStockModalOpen)}
        >
          Ajouter un nouvel article
        </Button>
      </div>
      <div className="mb-2">
        <Typography variant="h5" component="h1" gutterBottom>
          Gestion des Stocks
        </Typography>
        <Typography variant="subtitle1" color="textSecondary">
          Total: {filteredStocks.length} articles
          {!selectedStatus ? " actifs" : " supprimé"}
        </Typography>
      </div>

      <div>
        <StockFilters
          searchQuery={searchQuery}
          selectedStatus={selectedStatus}
          onSearchChange={setSearchQuery}
          onStatusChange={setSelectedStatus}
        />

        <ListDataGrid data={filteredStocks} type={"stock"} />
      </div>
      {isAddStockModalOpen && (
        <AddStocksModal
          isOpen={isAddStockModalOpen}
          handleClose={handleClose}
        />
      )}
    </div>
  );
};

export default Stock;
