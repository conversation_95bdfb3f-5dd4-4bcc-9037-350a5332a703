import { GetFacturationsByProfessionalIdUsecase } from "@/domain/usecases/professional/facturation/GetFacturationsByProfessionalIdUsecase.ts";
import { GetFacturationsByProfessionalIdRepository } from "@/infrastructure/repositories/facturation/GetFacturationByProfessionalIdRepository.ts";
import { useEffect, useState } from "react";

const Test = () => {
  const [data, setData] = useState(null);
  const repository = new GetFacturationsByProfessionalIdRepository();
  const usecase = new GetFacturationsByProfessionalIdUsecase(repository);

  useEffect(() => {
    const test = async () => {
      const data = await usecase.execute(315);
      setData(data);
    };
    test();
  }, []);

  return (
    <div>
      <h1>Test</h1>
      <p>{JSON.stringify(data)}</p>
    </div>
  );
};

export default Test;
