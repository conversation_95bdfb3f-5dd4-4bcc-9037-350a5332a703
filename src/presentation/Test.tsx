import { GetFacturationByPatientIdUsecase } from "@/domain/usecases/professional/facturation/GetFacturationsByPatientIdUsecase.ts";
import { GetFacturationByPatientIdRepository } from "@/infrastructure/repositories/facturation/GetFacturationByPatientIdRepository.ts";
import { useEffect, useState } from "react";

const Test = () => {
  const [data, setData] = useState(null);
  const repository = new GetFacturationByPatientIdRepository();
  const usecase = new GetFacturationByPatientIdUsecase(repository);

  useEffect(() => {
    const test = async () => {
      const data = await usecase.execute(315);
      setData(data);
    };
    test();
  }, []);

  return (
    <div>
      <h1>Test</h1>
      <p>{JSON.stringify(data)}</p>
    </div>
  );
};

export default Test;
