import { useMemo, useState } from "react";
import { FacturationDTO } from "@/domain/DTOS/FacturationDTO";

/**
 * Interface pour représenter un patient dans les filtres
 */
export interface PatientOption {
  id: number;
  nom: string;
  prenom: string;
}

/**
 * Hook personnalisé pour gérer la logique de filtrage des facturations
 * 
 * Ce hook centralise toute la logique métier liée au filtrage des facturations :
 * - Extraction des patients uniques depuis les facturations
 * - Gestion de l'état du patient sélectionné
 * - Filtrage des facturations selon le patient sélectionné
 * - Calcul du solde total des facturations filtrées
 * 
 * @param facturations - Liste des facturations à filtrer
 * @returns Objet contenant l'état et les fonctions de filtrage
 */
export const useFacturationFilters = (facturations: FacturationDTO[]) => {
  // État du patient sélectionné pour le filtrage
  const [selectedPatient, setSelectedPatient] = useState<PatientOption | null>(null);

  /**
   * Extrait la liste des patients uniques depuis les facturations
   * Évite les doublons en utilisant l'ID du patient comme clé unique
   */
  const availablePatients = useMemo((): PatientOption[] => {
    if (!facturations || facturations.length === 0) {
      return [];
    }

    const patientsMap = new Map<number, PatientOption>();
    
    facturations.forEach((facturation) => {
      const patient = facturation.patient;
      if (patient && !patientsMap.has(patient.id)) {
        patientsMap.set(patient.id, {
          id: patient.id,
          nom: patient.nom,
          prenom: patient.prenom,
        });
      }
    });

    return Array.from(patientsMap.values());
  }, [facturations]);

  /**
   * Filtre les facturations selon le patient sélectionné
   * Si aucun patient n'est sélectionné, retourne toutes les facturations
   */
  const filteredFacturations = useMemo((): FacturationDTO[] => {
    if (!facturations) {
      return [];
    }

    if (selectedPatient === null) {
      return facturations;
    }

    return facturations.filter((facturation) => 
      facturation.patient.id === selectedPatient.id
    );
  }, [facturations, selectedPatient]);

  /**
   * Calcule le solde total des facturations filtrées
   * Solde = Somme(montant - total_payé) pour chaque facturation
   */
  const totalBalance = useMemo((): number => {
    return filteredFacturations.reduce(
      (acc, facturation) => acc + (facturation.montant - facturation.total_paye),
      0
    );
  }, [filteredFacturations]);

  /**
   * Met à jour le patient sélectionné pour le filtrage
   * @param patient - Patient à sélectionner ou null pour réinitialiser le filtre
   */
  const handlePatientChange = (patient: PatientOption | null) => {
    setSelectedPatient(patient);
  };

  /**
   * Réinitialise le filtre (aucun patient sélectionné)
   */
  const clearFilter = () => {
    setSelectedPatient(null);
  };

  /**
   * Vérifie si un filtre est actuellement appliqué
   */
  const isFilterActive = selectedPatient !== null;

  return {
    // État
    selectedPatient,
    availablePatients,
    filteredFacturations,
    totalBalance,
    isFilterActive,
    
    // Actions
    handlePatientChange,
    clearFilter,
  };
};
