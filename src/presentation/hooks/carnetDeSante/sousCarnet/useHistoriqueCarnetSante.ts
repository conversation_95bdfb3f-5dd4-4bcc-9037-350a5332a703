import { HistoriqueCarnetSante } from "@/domain/models";
import { AppDispatch, RootState } from "@/store";
import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  createHistoriqueCarnetSante,
  getHistoriqueCarnetSantes,
  getHistoriqueCarnetSantePatient,
  updateHistoriqueCarnetSante,
  deleteHistoriqueCarnetSante,
  setSelectedHistoriqueCarnetSante,
  clearSelectedHistoriqueCarnetSante,
} from "@/application/slices/professionnal/historiqueCarnetSanteSlice";
import {
  action_carnet_de_sante_enum,
  utilisateurs_role_enum,
} from "@/domain/models/enums";
import { useAppSelector } from "../../redux";

export const useHistoriqueCarnetSante = () => {
  const dispatch = useDispatch<AppDispatch>();
  const {
    historiqueCarnetSantes,
    selectedHistoriqueCarnetSante,
    loading,
    error,
  } = useAppSelector((state) => state.historiqueCarnetSante);

  const { id: professionnalId, role } = useAppSelector(
    (state) => state.authentification.user
  );

  const create = useCallback(
    async (
      idCarnetSante: number,
      items: { id: number; detail: string }[],
      tableConcernee: string,
      typeAction: action_carnet_de_sante_enum
    ) => {
      const historiqueCarnetSanteData: Omit<HistoriqueCarnetSante, "id">[] =
        items.map((item) => ({
          id_carnet: idCarnetSante,
          id_professionnel: professionnalId,
          id_element_concerne: item.id,
          table_concernee: tableConcernee,
          type_action: typeAction,
          date_action: new Date(),
          details: item.detail,
        }));
      console.log(historiqueCarnetSanteData);
      await dispatch(createHistoriqueCarnetSante(historiqueCarnetSanteData));
    },
    [dispatch, professionnalId]
  );

  const get = useCallback(
    async (carnetId: number, tableConcernee: string) => {
      if (role === utilisateurs_role_enum.DASH) {
        await dispatch(getHistoriqueCarnetSantes({ carnetId, tableConcernee }));
      } else if (role === utilisateurs_role_enum.PROFESSIONNEL) {
        await dispatch(
          getHistoriqueCarnetSantePatient({ carnetId, tableConcernee })
        );
      }
    },
    [dispatch]
  );

  const update = useCallback(
    async (id: number, data: Partial<HistoriqueCarnetSante>) => {
      await dispatch(updateHistoriqueCarnetSante({ id, data }));
    },
    [dispatch]
  );

  const remove = useCallback(
    async (id: number) => {
      await dispatch(deleteHistoriqueCarnetSante(id));
    },
    [dispatch]
  );

  const select = useCallback(
    (historiqueCarnetSante: HistoriqueCarnetSante | null) => {
      dispatch(setSelectedHistoriqueCarnetSante(historiqueCarnetSante));
    },
    [dispatch]
  );

  const clearSelected = useCallback(() => {
    dispatch(clearSelectedHistoriqueCarnetSante());
  }, [dispatch]);

  return {
    historiqueCarnetSantes,
    selectedHistoriqueCarnetSante,
    loading,
    error,
    create,
    get,
    update,
    remove,
    select,
    clearSelected,
  };
};
