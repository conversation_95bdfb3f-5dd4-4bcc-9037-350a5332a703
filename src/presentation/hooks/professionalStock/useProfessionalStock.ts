import { useCallback } from "react";
import { useAppDispatch, useAppSelector } from "../redux";
import { Stocks } from "@/domain/models";
import {
  deleteStocksByProfessionalIdSlice,
  createStocksByProfessionalIdSlice,
  getStocksByProfessionalIdSlice,
  updateStocksByProfessionalIdSlice,
  setFormData as setFormDataAction,
  FormDataKey,
  initializeData as initializeDataAction,
  resetState as resetStateAction,
  setFormErrors as setFormErrorsAction,
  FormData,
} from "@/application/slices/professionnal/professionalStockSlice";
import { stockFormSchema } from "@/presentation/components/features/professional/stock/StockFormShema";

export const useProfessionalStock = () => {
  const dispatch = useAppDispatch();
  const { stocks, formData, loading, error, formErrors } = useAppSelector(
    (state) => state.professionalStock,
  );
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id,
  );

  const handleGetStocksByProfessionalId = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.",
        );
      }
      return await dispatch(getStocksByProfessionalIdSlice(id)).unwrap();
    },
    [dispatch],
  );

  const handleCreateStocksProfessional = useCallback(
    async (stockData: Omit<Stocks, "id">[]) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.",
        );
      }
      await dispatch(
        createStocksByProfessionalIdSlice([...stockData]),
      ).unwrap();
    },
    [dispatch],
  );

  const handleUpdateStocksProfessional = useCallback(
    async (stockId: number, data: Partial<Stocks>) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.",
        );
      }
      await dispatch(
        updateStocksByProfessionalIdSlice({ stockId, data }),
      ).unwrap();
    },
    [dispatch],
  );

  const handleDeleteStocksProfessional = useCallback(
    async (id: number) => {
      if (!navigator.onLine) {
        throw new Error(
          "Pas de connexion internet. Veuillez vérifier votre connexion et réessayer.",
        );
      }
      await dispatch(
        deleteStocksByProfessionalIdSlice({
          professionalId: id,
          estSupprimee: true,
        }),
      ).unwrap();
    },
    [dispatch],
  );

  const validateFormData = <K extends FormDataKey>(
    key: K,
    value: FormData[K],
  ) => {
    const shape = stockFormSchema.shape;

    if (!shape[key]) {
      console.warn(`Champ inconnu dans le schéma : "${key}"`);
      return true; // on ignore les champs non-validés
    }

    const fieldSchema = shape[key];

    const result = fieldSchema.safeParse(value);

    if (!result.success) {
      const message = result.error.issues[0]?.message ?? "Champ invalide";
      dispatch(setFormErrorsAction({ ...formErrors, [key]: message }));
      return false;
    }

    // Supprimer l’erreur si le champ est correct
    const { [key]: _, ...rest } = formErrors;
    dispatch(setFormErrorsAction(rest));
    return true;
  };

  const isFormDataValid = () => {
    const result = stockFormSchema.safeParse(formData);
    if (!result.success) {
      // 🧠 On reconstruit un objet des erreurs champ-par-champ
      const fieldErrors = result.error.flatten().fieldErrors;
      const formattedErrors = Object.entries(fieldErrors).reduce(
        (acc, [key, val]) => {
          if (val && val.length > 0) {
            acc[key as FormDataKey] = val[0]; // On ne prend que le 1er message
          }
          return acc;
        },
        {} as Partial<Record<FormDataKey, string>>,
      );

      dispatch(setFormErrorsAction(formattedErrors));
      return false;
    }

    return true;
  };

  const initializeState = (id: number) => {
    const matchingStock = stocks.find((stock) => stock.id === id);
    dispatch(initializeDataAction({ matchingStock: matchingStock }));
  };

  const setFormErrors = (key: FormDataKey, value: string) => {
    dispatch(setFormErrorsAction({ ...formErrors, [key]: value }));
  };

  const setFormData = <K extends FormDataKey>(key: K, value: FormData[K]) => {
    dispatch(setFormDataAction({ key, value }));
  };

  const resetState = () => {
    dispatch(resetStateAction());
  };
  return {
    loading,
    error,
    formErrors,
    validateFormData,
    isFormDataValid,
    stocks,
    formData,
    setFormData,
    professionalId,
    handleGetStocksByProfessionalId,
    handleCreateStocksProfessional,
    handleUpdateStocksProfessional,
    handleDeleteStocksProfessional,
    initializeState,
    resetState,
    setFormErrors,
  };
};
