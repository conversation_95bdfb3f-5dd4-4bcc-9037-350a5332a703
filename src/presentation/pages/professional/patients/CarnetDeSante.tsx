import { ComponentProps, useState } from "react";
import CarnetDeSanteCard from "@/presentation/components/common/historiqueCarnetSante/component/CarnetDeSanteCard";
import { AddCarnetDeSanteModal } from "@/presentation/components/common/Modal/AddCarnetDeSanteModal";
import { useParams } from "react-router-dom";
import {
  useCarnetDeSanteData,
  useCarnetDeSante,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import LoadingSpinner from "@/presentation/components/common/LoadingSpinner";
import { Button } from "@mui/material";
import { getDataCard } from "@/shared/constants/cardData";
import { HistoriqueCarnetDeSanteModal } from "@/presentation/components/common/Modal/HistoriqueCarnetDeSanteModal";
import { PrintService } from "@/domain/services/PrintService";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import { PRIMARY } from "@/shared/constants/Color";
import { TITRES_CARNET_DE_SANTE } from "@/shared/constants/TitreCarnetDeSante";
import { useDiagnostic } from "@/presentation/hooks/carnetDeSante/sousCarnet/useDiagnostic";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useHistoriqueCarnetSante } from "@/presentation/hooks/carnetDeSante/sousCarnet/useHistoriqueCarnetSante";
import { twMerge } from "tailwind-merge";
import { useAppSelector } from "@/presentation/hooks/redux";
import { utilisateurs_role_enum } from "@/domain/models/enums";
import { EditConfidentialiteModal } from "@/presentation/components/common/Modal/EditConfidentialiteModal";
import { useEmployer } from "@/presentation/hooks/employer/useEmployer";
import { CarnetSante } from "@/domain/models";

type CarnetDeSanteProps = ComponentProps<"div"> & {
  isEdit?: boolean;
};

const CarnetDeSante: React.FC<CarnetDeSanteProps> = ({
  isEdit,
  className,
  ...props
}) => {
  const [isCarnetDeSanteModalOpen, setIsCarnetDeSanteModalOpen] =
    useState(false);
  const [isHistoriqueCarnetDeSanteModal, setIsHistoriqueCarnetDeSanteModal] =
    useState(false);
  const [type, setType] = useState("");
  const { id } = useParams();
  const role = useAppSelector((state) => state.authentification.user?.role);
  const id_user_connected = useAppSelector(
    (state) => state.authentification.userData?.id
  );
  const {
    idCarnetSante,
    loading: loadingIdCarnet,
    data,
    handleSubmit,
  } = useCarnetDeSanteData(
    role === utilisateurs_role_enum.PROFESSIONNEL ||
      role === utilisateurs_role_enum.DASH
      ? Number(id)
      : id_user_connected
  );
  const { resetState } = useDiagnostic();
  const { setConfidentialite, resetSelectedFileState } =
    useCarnetDeSanteState();

  const { selectedDataProfessionalPatient } = useProfessionnelPatient();
  const { selectedEmployerSlice } = useEmployer();
  const { currentProfessional } = useSearchProfessional();

  const { loading: loadingCreatCarnet, resetSearch } = useCarnetDeSante();
  const { clearSelected } = useHistoriqueCarnetSante();
  const handleIsCarnetDeSanteModalOpen = (type: string) => {
    setIsCarnetDeSanteModalOpen(true);
    setType(type);
  };

  const handleIsHistoriqueCarnetDeSanteModalOpen = (type: string) => {
    setIsHistoriqueCarnetDeSanteModal(true);
    setType(type);
  };

  const handleCloseCarnetDeSanteModal = () => {
    setIsCarnetDeSanteModalOpen(false);
    if (type === TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage) {
      resetState();
      resetSelectedFileState();
    } else {
      resetSearch();
    }
    if (isEdit) {
      setConfidentialite(false);
    }
  };

  const handleCloseHistoriqueCarnetDeSanteModal = () => {
    setIsHistoriqueCarnetDeSanteModal(false);
    // resetSearch();
    clearSelected();
  };

  const DATA_CARD = getDataCard(
    data,
    role === utilisateurs_role_enum.DASH
      ? selectedEmployerSlice?.sexe
      : selectedDataProfessionalPatient?.patient.sexe
  );

  const DATA_CARD_DIAGNOSTICS = getDataCard(
    data,
    TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
  );

  const handlePrint = () => {
    PrintService.printCarnetSante(
      [...DATA_CARD, ...DATA_CARD_DIAGNOSTICS],
      selectedDataProfessionalPatient?.patient,
      currentProfessional
    );
  };

  return (
    <>
      {idCarnetSante ? (
        <div>
          <div className="flex items-center my-4">
            <h2 className="text-xl font-semibold ml-2">Carnet de sante</h2>
            {role === utilisateurs_role_enum.PROFESSIONNEL && (
              <Button
                variant="outlined"
                color="primary"
                sx={{ textTransform: "none", ml: 2, borderRadius: 5 }}
                onClick={handlePrint}
              >
                IMPRIMER
              </Button>
            )}
          </div>
          <div className={twMerge(className)} {...props}>
            {DATA_CARD.map((data, index) => (
              <CarnetDeSanteCard
                key={index}
                data={data}
                isEdit={isEdit}
                setIsCarnetDeSanteModalOpen={() =>
                  handleIsCarnetDeSanteModalOpen(data.title)
                }
                setIsHistoriqueCarnetDeSanteModalOpen={() =>
                  handleIsHistoriqueCarnetDeSanteModalOpen(data.title)
                }
              />
            ))}
          </div>
          <div className="mt-5">
            <CarnetDeSanteCard
              data={DATA_CARD_DIAGNOSTICS[0]}
              isEdit={isEdit}
              setIsCarnetDeSanteModalOpen={() =>
                handleIsCarnetDeSanteModalOpen(
                  TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
                )
              }
              setIsHistoriqueCarnetDeSanteModalOpen={() =>
                handleIsHistoriqueCarnetDeSanteModalOpen(
                  TITRES_CARNET_DE_SANTE.testsMedicauxDiagnosticsEtDepistage
                )
              }
            />
          </div>
          {isCarnetDeSanteModalOpen &&
            (isEdit ? (
              <EditConfidentialiteModal
                type={type}
                isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
                handleCloseModal={handleCloseCarnetDeSanteModal}
              />
            ) : (
              <AddCarnetDeSanteModal
                type={type}
                isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
                handleCloseModal={handleCloseCarnetDeSanteModal}
              />
            ))}
          {isHistoriqueCarnetDeSanteModal && (
            <HistoriqueCarnetDeSanteModal
              type={type}
              isHistoriqueCarnetDeSanteModalOpen={
                isHistoriqueCarnetDeSanteModal
              }
              handleCloseModal={handleCloseHistoriqueCarnetDeSanteModal}
            />
          )}
        </div>
      ) : (
        <div className="flex items-center justify-center">
          {loadingIdCarnet ? (
            <LoadingSpinner />
          ) : (
            <Button
              variant="contained"
              sx={{
                textTransform: "none",
                mt: 2,
                backgroundColor: PRIMARY,
              }}
              onClick={() => {
                const data: Omit<CarnetSante, "id"> = {
                  id_proprietaire: Number(id),
                  date_creation: new Date(),
                  confidentialite: false,
                };
                handleSubmit(data);
              }}
              loading={loadingCreatCarnet}
            >
              Creer le carnet de sante
            </Button>
          )}
        </div>
      )}
    </>
  );
};

export default CarnetDeSante;
