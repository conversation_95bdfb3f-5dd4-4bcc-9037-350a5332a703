import CarnetDeSanteCard from "@/presentation/components/common/historiqueCarnetSante/component/CarnetDeSanteCard";
import ConsultationMedicaleCard from "@/presentation/components/common/historiqueCarnetSante/component/ConsultationMedicaleCard";
import { AddCarnetDeSanteModal } from "@/presentation/components/common/Modal/AddCarnetDeSanteModal";
import { ConsultationForm } from "@/presentation/pages/professional/patients/ConsultationForm";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useMedicalConsultation } from "@/presentation/hooks/consultationMedicale";
import {
  useCarnetDeSante,
  useCarnetDeSanteData,
  useCarnetDeSanteState,
} from "@/presentation/hooks/carnetDeSante";
import { useConsultationForm } from "@/presentation/hooks/consultationMedicale/useConsultationForm";
import { Box, Button, Divider } from "@mui/material";
import { useState } from "react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { PRIMARY } from "@/shared/constants/Color";
import { getDataCard } from "@/shared/constants/cardData";
import { active_tab_enum } from "@/domain/models/enums";
import { ALLERGIE_TABLE_NAME } from "@/infrastructure/repositories/allergie/Constant";
import { AFFECTATION_MEDICAL_TABLE_NAME } from "@/infrastructure/repositories/affectationMedicale/Constant";
import { MEDICAMENT_TABLE_NAME } from "@/infrastructure/repositories/medicament/Constant";
import { BookAppointmentModal } from "@/presentation/components/common/Modal/BookAppointmentModal";
import { useAgendaState } from "@/presentation/hooks/agenda";
import { PrintService } from "@/domain/services/PrintService";
import useSearchProfessional from "@/presentation/hooks/use-search-professional";
import { useProfessionnelPatient } from "@/presentation/hooks/use-professionnelPatient";
import useAppointment from "@/presentation/hooks/appointment/use-appointment";

const ConsultationMedicale = () => {
  const [consultationDetail, setConsultationDetail] = useState<{
    id: number;
    date_visite: Date;
  }>(null);
  const { isAddForm, setIsAddForm } = useCarnetDeSanteState();
  const [isCarnetDeSanteModalOpen, setIsCarnetDeSanteModalOpen] =
    useState(false);
  const { isAppointmentModalOpen, handleIsAppointmentModalOpen } =
    useAgendaState();
  const [type, setType] = useState("");
  const { idCarnetSante, resetSearch } = useCarnetDeSante();
  const { consultations, data, signeVitaux } = useCarnetDeSanteData();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.id,
  );
  const handleIsCarnetDeSanteModalOpen = (type: string) => {
    setIsCarnetDeSanteModalOpen(true);
    setType(type);
  };
  const handleCloseCarnetDeSanteModal = () => {
    setIsCarnetDeSanteModalOpen(false);
    resetSearch();
  };
  const {
    loading,
    handleCreateMedicalConsultation,
    handleUpdateMedicalConsultation,
  } = useMedicalConsultation();

  const {
    formData,
    isFormValid,
    getConsultationData,
    resetForm,
    initialiseState,
  } = useConsultationForm(professionalId, idCarnetSante);

  const { terminateAppointment, selectedAppointment } = useAppointment();

  const { currentProfessional } = useSearchProfessional();

  const { selectedDataProfessionalPatient } = useProfessionnelPatient();

  const handlePrint = () => {
    PrintService.printConsultation({
      carnet: data,
      signeVitaux: signeVitaux[signeVitaux.length - 1],
      consultations: consultations,
      patient: selectedDataProfessionalPatient?.patient,
      professionnel: currentProfessional,
    });
  };

  const handleSubmit = async () => {
    const data = getConsultationData();
    if (consultationDetail) {
      await handleUpdateMedicalConsultation(consultationDetail.id, data);
    } else {
      await handleCreateMedicalConsultation(data);
    }

    await terminateAppointment(selectedAppointment.id);
    handleBack();
  };

  const handleBack = () => {
    resetForm();
    setIsAddForm(false);
    setConsultationDetail(null);
  };

  const DATA_CARD = getDataCard(data, active_tab_enum.consultationMedicale, [
    AFFECTATION_MEDICAL_TABLE_NAME,
    MEDICAMENT_TABLE_NAME,
    ALLERGIE_TABLE_NAME,
  ]);

  return (
    <div>
      {isAddForm ? (
        <div className="my-4">
          <Button
            variant="contained"
            color="primary"
            sx={{
              textTransform: "none",
              ml: 2,
              borderRadius: 5,
              backgroundColor: PRIMARY,
            }}
            onClick={handleBack}
          >
            retour
          </Button>
          <div className="flex items-center my-4">
            {/* <NotebookPen className="w-6 text-primary" /> */}
            <h2 className="text-xl font-semibold ml-2">
              {format(
                consultationDetail
                  ? new Date(consultationDetail.date_visite)
                  : new Date(),
                "d MMMM yyyy",
                {
                  locale: fr,
                },
              )}
            </h2>
            <Button
              variant="outlined"
              sx={{
                textTransform: "none",
                ml: 2,
                borderRadius: 5,
              }}
              onClick={handlePrint}
            >
              IMPRIMER
            </Button>
          </div>
          <Divider />
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 my-4">
            <div className="lg:col-span-2 space-y-6">
              <ConsultationForm
                {...formData}
                date_consultation={
                  consultationDetail
                    ? new Date(consultationDetail.date_visite)
                    : new Date()
                }
              />
            </div>
            <div className="lg:col-span-1 h-full flex items-center">
              {/* <Divider orientation="vertical" flexItem/> */}
              <div className="w-full lg:h-[calc(100vh-90px)] overflow-y-auto">
                {DATA_CARD.map((data, index) => (
                  <CarnetDeSanteCard
                    key={index}
                    data={data}
                    setIsCarnetDeSanteModalOpen={() =>
                      handleIsCarnetDeSanteModalOpen(data.title)
                    }
                    className="lg:mx-2 my-2"
                  />
                ))}
              </div>
              {isCarnetDeSanteModalOpen && (
                <AddCarnetDeSanteModal
                  type={type}
                  isCarnetDeSanteModalOpen={isCarnetDeSanteModalOpen}
                  handleCloseModal={handleCloseCarnetDeSanteModal}
                />
              )}
            </div>
          </div>
          <Divider />
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mt: 4,
            }}
          >
            <Box>
              <Button
                variant="contained"
                color="primary"
                sx={{
                  textTransform: "none",
                  ml: 2,
                  borderRadius: 5,
                  backgroundColor: PRIMARY,
                }}
                onClick={() => handleIsAppointmentModalOpen(true)}
              >
                Prochain rendez-vous
              </Button>
            </Box>
            <Box>
              <Button
                variant="outlined"
                color="primary"
                sx={{ textTransform: "none", ml: 2, borderRadius: 5 }}
                onClick={handleBack}
              >
                Annuler
              </Button>
              <Button
                variant="contained"
                color="primary"
                sx={{
                  textTransform: "none",
                  ml: 2,
                  borderRadius: 5,
                  backgroundColor: PRIMARY,
                }}
                onClick={handleSubmit}
                loading={loading}
                disabled={!isFormValid}
              >
                Enregistrer
              </Button>
            </Box>
          </Box>
          {isAppointmentModalOpen && <BookAppointmentModal />}
        </div>
      ) : (
        <>
          <div className="flex items-center gap-2 my-4">
            <h2 className="text-xl font-semibold">Consultation médicale</h2>
            <Button
              variant="contained"
              sx={{
                textTransform: "none",
                ml: 2,
                borderRadius: 5,
                backgroundColor: PRIMARY,
              }}
              onClick={() => setIsAddForm(true)}
            >
              Ajouter une note medicale
            </Button>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-4 md:grid-cols-3 gap-4">
            {consultations?.map((consultation) => (
              <ConsultationMedicaleCard
                key={consultation.id}
                onClick={() => {
                  initialiseState(consultation);
                  setConsultationDetail({
                    id: consultation.id,
                    date_visite: consultation.date_visite,
                  });
                  setIsAddForm(true);
                }}
                consultation={consultation}
                className="cursor-pointer"
              />
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default ConsultationMedicale;
