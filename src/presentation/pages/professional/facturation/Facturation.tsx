import { useEffect } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { Typography } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import {
  useFacturation,
  useFacturationFilters,
} from "@/presentation/hooks/facturation";
import FacturationFilters from "@/presentation/components/common/historiqueCarnetSante/component/FacturationFilters";

/**
 * Page de gestion des facturations pour les professionnels
 *
 * Cette page affiche la liste des facturations d'un professionnel avec
 * la possibilité de les filtrer par patient. Elle utilise le hook
 * useFacturationFilters pour gérer toute la logique métier.
 */
const Facturation = () => {
  // Récupération des données de facturation
  const {
    listeFacturationProfessional,
    getFacturationsByProfessionalId,
    loading,
  } = useFacturation();

  // ID du professionnel connecté
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.utilisateur_id
  );

  // Hook de filtrage qui gère toute la logique métier
  const {
    selectedPatient,
    availablePatients,
    filteredFacturations,
    totalBalance,
    handlePatientChange,
  } = useFacturationFilters(listeFacturationProfessional || []);

  // Chargement des facturations au montage du composant
  useEffect(() => {
    if (professionalId) {
      getFacturationsByProfessionalId(professionalId);
    }
  }, [professionalId, getFacturationsByProfessionalId]);

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 my-4">
        <Typography variant="subtitle1" color="textSecondary">
          Solde total: {totalBalance} MGA
        </Typography>
        <div className="flex justify-end">
          <FacturationFilters
            selectedPatient={selectedPatient}
            availablePatients={availablePatients}
            onPatientChange={handlePatientChange}
            loading={loading}
          />
        </div>
      </div>
      <div>
        <ListDataGrid
          data={filteredFacturations}
          type={"facturation_professional"}
        />
      </div>
    </div>
  );
};

export default Facturation;
