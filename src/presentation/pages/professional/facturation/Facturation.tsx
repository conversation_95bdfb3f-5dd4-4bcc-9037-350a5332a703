import { useEffect, useMemo, useState } from "react";
import ListDataGrid from "@/presentation/components/common/listDataGrid/ListDataGrid";
import { Typography } from "@mui/material";
import { useAppSelector } from "@/presentation/hooks/redux";
import { useFacturation } from "@/presentation/hooks/facturation";
import FacturationFilters from "@/presentation/components/common/historiqueCarnetSante/component/FacturationFilters";

const Facturation = () => {
  const [selectedPatient, setSelectedPatient] = useState<{
    id: number;
    nom: string;
    prenom: string;
  }>(null);

  const { listeFacturationProfessional, getFacturationsByProfessionalId } =
    useFacturation();
  const professionalId = useAppSelector(
    (state) => state.authentification.userData?.utilisateur_id
  );

  const filteredFacturation = useMemo(() => {
    return listeFacturationProfessional.filter((fact) => {
      const matchesPatient =
        selectedPatient === null || fact.id_patient === selectedPatient.id;

      return matchesPatient;
    });
  }, [listeFacturationProfessional, selectedPatient]);

  useEffect(() => {
    if (professionalId) {
      getFacturationsByProfessionalId(professionalId);
    }
  }, [professionalId]);

  return (
    <div>
      <div className="grid grid-cols-1 md:grid-cols-2 my-4">
        <Typography variant="subtitle1" color="textSecondary">
          Solde total:{" "}
          {filteredFacturation.reduce(
            (acc, fact) => acc + (fact.montant - fact.total_paye),
            0
          )}{" "}
          MGA
        </Typography>
        <div className="flex justify-end">
          <FacturationFilters
            selectedPatient={selectedPatient}
            onSelectedPatientChange={setSelectedPatient}
          />
        </div>
      </div>
      <div>
        <ListDataGrid
          data={filteredFacturation}
          type={"facturation_professional"}
        />
      </div>
    </div>
  );
};

export default Facturation;
