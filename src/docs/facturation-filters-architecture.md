# Architecture du système de filtrage des facturations

## Vue d'ensemble

Le système de filtrage des facturations a été refactorisé pour suivre les bonnes pratiques d'architecture React avec une séparation claire des responsabilités entre la logique métier et la présentation.

## Architecture

### 1. Hook personnalisé : `useFacturationFilters`

**Localisation :** `src/presentation/hooks/facturation/useFacturationFilters.ts`

**Responsabilités :**
- Gestion de l'état du patient sélectionné
- Extraction des patients uniques depuis les facturations
- Filtrage des facturations selon le patient sélectionné
- Calcul du solde total des facturations filtrées
- Fourniture des actions de filtrage (sélection, réinitialisation)

**API du hook :**
```typescript
const {
  selectedPatient,        // Patient actuellement sélectionné
  availablePatients,      // Liste des patients disponibles
  filteredFacturations,   // Facturations filtrées
  totalBalance,          // Solde total calculé
  isFilterActive,        // Indique si un filtre est actif
  handlePatientChange,   // Action pour changer le patient
  clearFilter,          // Action pour réinitialiser le filtre
} = useFacturationFilters(facturations);
```

### 2. Composant de présentation : `FacturationFilters`

**Localisation :** `src/presentation/components/common/historiqueCarnetSante/component/FacturationFilters.tsx`

**Responsabilités :**
- Affichage de l'interface utilisateur de filtrage
- Gestion des interactions utilisateur (sélection, recherche)
- Présentation de la liste des patients disponibles
- Aucune logique métier

**Props :**
```typescript
interface FacturationFiltersProps {
  selectedPatient: PatientOption | null;
  availablePatients: PatientOption[];
  onPatientChange: (patient: PatientOption | null) => void;
  loading?: boolean;
}
```

### 3. Page principale : `Facturation`

**Localisation :** `src/presentation/pages/professional/facturation/Facturation.tsx`

**Responsabilités :**
- Orchestration de l'affichage des données
- Chargement des facturations
- Coordination entre le hook de filtrage et les composants de présentation
- Aucune logique métier de filtrage

## Flux de données

```
1. Facturation.tsx charge les facturations via useFacturation()
2. useFacturationFilters() traite les facturations et fournit les données filtrées
3. FacturationFilters.tsx affiche l'interface de filtrage
4. L'utilisateur sélectionne un patient
5. useFacturationFilters() met à jour les données filtrées
6. ListDataGrid affiche les facturations filtrées
7. Le solde total est automatiquement recalculé
```

## Avantages de cette architecture

### Séparation des responsabilités
- **Logique métier** : Centralisée dans le hook `useFacturationFilters`
- **Présentation** : Isolée dans les composants React
- **Orchestration** : Gérée par le composant page principal

### Réutilisabilité
- Le hook `useFacturationFilters` peut être réutilisé dans d'autres contextes
- Le composant `FacturationFilters` est découplé de la logique métier

### Testabilité
- La logique métier peut être testée indépendamment
- Les composants peuvent être testés avec des props mockées

### Maintenabilité
- Code plus lisible et organisé
- Modifications de la logique métier centralisées
- Interface utilisateur découplée de la logique

## Fonctionnalités

### Filtrage par patient
- Sélection d'un patient dans une liste déroulante
- Filtrage en temps réel des facturations
- Possibilité de réinitialiser le filtre

### Calcul automatique du solde
- Mise à jour automatique du solde total selon les facturations filtrées
- Formule : `Somme(montant - total_payé)`

### Interface utilisateur
- Autocomplete avec recherche
- Affichage des avatars des patients
- États de chargement
- Messages d'aide (aucun patient trouvé, etc.)

## Utilisation

### Dans un composant
```typescript
import { useFacturationFilters } from '@/presentation/hooks/facturation';

const MonComposant = () => {
  const facturations = // ... récupération des facturations
  
  const {
    selectedPatient,
    availablePatients,
    filteredFacturations,
    totalBalance,
    handlePatientChange,
  } = useFacturationFilters(facturations);

  return (
    <div>
      <FacturationFilters
        selectedPatient={selectedPatient}
        availablePatients={availablePatients}
        onPatientChange={handlePatientChange}
      />
      {/* Affichage des facturations filtrées */}
    </div>
  );
};
```

## Types

### PatientOption
```typescript
interface PatientOption {
  id: number;
  nom: string;
  prenom: string;
}
```

Cette interface représente un patient dans le contexte du filtrage et est exportée depuis le hook pour éviter la duplication.
